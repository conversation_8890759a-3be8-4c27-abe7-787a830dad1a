package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@TableName("build_environment_storage")
public class BuildEnvironmentStorageDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 8036395417463990923L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("environment_id")
    private Integer environmentId;

    /**
     * 构建任务Id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * minio存储使用量 KB
     */
    @TableField("used_storage")
    private Integer usedStorage;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
